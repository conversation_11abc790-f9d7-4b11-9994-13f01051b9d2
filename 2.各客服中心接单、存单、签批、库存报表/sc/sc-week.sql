-- 各客服中心本周计划物控中心存单报表 - 独立变量版本
SELECT
    -- 一中心计划物控中心存单
    ISNULL((SELECT COUNT(*) FROM dbo.ODS_T5_vscdd_dzbh
            WHERE scAddr = '一中心' AND pzbz = 'false' AND stoped = 'false' AND canceled = 'false'
            AND DATEPART(week, dzrq) = DATEPART(week, GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 一中心_本周_计划物控中心存单,

    -- 二中心计划物控中心存单
    ISNULL((SELECT COUNT(*) FROM dbo.ODS_T5_vscdd_dzbh
            WHERE scAddr = '二中心' AND pzbz = 'false' AND stoped = 'false' AND canceled = 'false'
            AND DATEPART(week, dzrq) = DATEPART(week, GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 二中心_本周_计划物控中心存单,

    -- 新材中心计划物控中心存单
    ISNULL((SELECT COUNT(*) FROM dbo.ODS_T5_vscdd_dzbh
            WHERE scAddr = '新材中心' AND pzbz = 'false' AND stoped = 'false' AND canceled = 'false'
            AND DATEPART(week, dzrq) = DATEPART(week, GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 新材中心_本周_计划物控中心存单,

    -- 其他计划物控中心存单
    ISNULL((SELECT COUNT(*) FROM dbo.ODS_T5_vscdd_dzbh
            WHERE scAddr NOT IN ('一中心', '二中心', '新材中心') AND pzbz = 'false' AND stoped = 'false' AND canceled = 'false'
            AND DATEPART(week, dzrq) = DATEPART(week, GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 其他_本周_计划物控中心存单

