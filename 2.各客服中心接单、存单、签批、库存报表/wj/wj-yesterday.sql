-- 各客服中心昨日违建库存报表 - 按库存天数区间统计
-- 主表筛选jabz为false的记录，使用jarq获取日期，通过关联获取scAddr
SELECT
    -- 一中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.jabz = 'false'
            AND b.scAddr = '一中心'
            AND CONVERT(date, a.jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 一中心_昨天_违建库存,

    -- 二中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.jabz = 'false'
            AND b.scAddr = '二中心'
            AND CONVERT(date, a.jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 二中心_昨天_违建库存,

    -- 新材中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.jabz = 'false'
            AND b.scAddr = '新材中心'
            AND CONVERT(date, a.jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 新材中心_昨天_违建库存,

    -- 其他中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.jabz = 'false'
            AND b.scAddr NOT IN ('一中心', '二中心', '新材中心')
            AND CONVERT(date, a.jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 其他_昨天_违建库存