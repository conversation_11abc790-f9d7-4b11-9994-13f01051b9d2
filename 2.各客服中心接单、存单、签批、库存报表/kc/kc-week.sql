-- 各客服中心本周库存报表 - 按库存天数区间统计
-- 通过关联dbo.ODS_T5_HJLY_VXSDD_LIST表获取订单日期
SELECT
    -- 一中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE b.scAddr = '一中心'
            AND DATEPART(week, b.dzrq) = DATEPART(week, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 一中心_本周_库存,

    -- 二中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE b.scAddr = '二中心'
            AND DATEPART(week, b.dzrq) = DATEPART(week, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 二中心_本周_库存,

    -- 新材中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE b.scAddr = '新材中心'
            AND DATEPART(week, b.dzrq) = DATEPART(week, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 新材中心_本周_库存,

    -- 其他中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE b.scAddr NOT IN ('一中心', '二中心', '新材中心')
            AND DATEPART(week, b.dzrq) = DATEPART(week, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 其他_本周_库存