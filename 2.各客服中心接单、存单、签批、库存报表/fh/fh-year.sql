
-- 各客服中心本年发货报表 - 理重总量统计(单位:吨)
-- 通过关联dbo.ODS_T5_vscdd_dzbh表获取scAddr，使用dzid关联xsdzid，统计llwt重量并转换为吨
SELECT
    -- 一中心数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_vscdd_dzbh b ON a.dzid = b.xsdzid
            WHERE b.scAddr = '一中心'
            AND CONVERT(date, a.dzrq) >= CONVERT(date, DATEADD(year, DATEDIFF(year, 0, GETDATE()), 0))
            AND CONVERT(date, a.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 一中心_本年_发货重量_吨,

    -- 二中心数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_vscdd_dzbh b ON a.dzid = b.xsdzid
            WHERE b.scAddr = '二中心'
            AND CONVERT(date, a.dzrq) >= CONVERT(date, DATEADD(year, DATEDIFF(year, 0, GETDATE()), 0))
            AND CONVERT(date, a.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 二中心_本年_发货重量_吨,

    -- 新材中心数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_vscdd_dzbh b ON a.dzid = b.xsdzid
            WHERE b.scAddr = '新材中心'
            AND CONVERT(date, a.dzrq) >= CONVERT(date, DATEADD(year, DATEDIFF(year, 0, GETDATE()), 0))
            AND CONVERT(date, a.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 新材中心_本年_发货重量_吨,

    -- 其他中心数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_vscdd_dzbh b ON a.dzid = b.xsdzid
            WHERE b.scAddr NOT IN ('一中心', '二中心', '新材中心')
            AND CONVERT(date, a.dzrq) >= CONVERT(date, DATEADD(year, DATEDIFF(year, 0, GETDATE()), 0))
            AND CONVERT(date, a.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 其他_本年_发货重量_吨

