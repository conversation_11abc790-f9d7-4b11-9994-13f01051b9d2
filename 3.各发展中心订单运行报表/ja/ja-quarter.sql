-- 各发展中心本季度结案库存报表 - 按结案天数区间统计
-- 根据生产结案天数yjaDays划分区间：<30天、30-90天、90-180天、>180天
-- 通过关联dbo.ODS_T5_HJLY_VXSDD_LIST表获取dzrq日期信息
SELECT
    -- 建材发展中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '建材业务服务中心' AND a.yjaDays < 30
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 建材发展中心_本季度_结案库存_小于30天,

    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '建材业务服务中心' AND a.yjaDays >= 30 AND a.yjaDays < 90
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 建材发展中心_本季度_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '建材业务服务中心' AND a.yjaDays >= 90 AND a.yjaDays < 180
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 建材发展中心_本季度_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '建材业务服务中心' AND a.yjaDays >= 180
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 建材发展中心_本季度_结案库存_大于180天,

    -- 海外事业发展中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '海外事业发展' AND a.yjaDays < 30
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 海外事业发展中心_本季度_结案库存_小于30天,

    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '海外事业发展' AND a.yjaDays >= 30 AND a.yjaDays < 90
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 海外事业发展中心_本季度_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '海外事业发展' AND a.yjaDays >= 90 AND a.yjaDays < 180
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 海外事业发展中心_本季度_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '海外事业发展' AND a.yjaDays >= 180
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 海外事业发展中心_本季度_结案库存_大于180天,

    -- 新材发展中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '新材料业务部' AND a.yjaDays < 30
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 新材发展中心_本季度_结案库存_小于30天,

    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '新材料业务部' AND a.yjaDays >= 30 AND a.yjaDays < 90
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 新材发展中心_本季度_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '新材料业务部' AND a.yjaDays >= 90 AND a.yjaDays < 180
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 新材发展中心_本季度_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '新材料业务部' AND a.yjaDays >= 180
            AND DATEPART(quarter, b.dzrq) = DATEPART(quarter, GETDATE()) AND YEAR(b.dzrq) = YEAR(GETDATE())), 0) AS 新材发展中心_本季度_结案库存_大于180天


