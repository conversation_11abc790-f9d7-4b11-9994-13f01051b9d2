-- 各发展中心昨日库存报表 - 按库存天数区间统计
-- 通过关联dbo.ODS_T5_HJLY_VXSDD_LIST表获取dzrq日期信息
SELECT
    -- 建材业务服务中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '建材业务服务中心'
            AND CONVERT(date, b.dzrq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 建材业务服务中心_昨天_库存,

    -- 海外事业发展数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '海外事业发展'
            AND CONVERT(date, b.dzrq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 海外事业发展_昨天_库存,

    -- 新材料业务部数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '新材料业务部'
            AND CONVERT(date, b.dzrq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 新材料业务部_昨天_库存