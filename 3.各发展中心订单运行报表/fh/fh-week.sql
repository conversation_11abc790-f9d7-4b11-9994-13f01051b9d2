-- 各发展中心本周发货报表 - 理重总量统计(单位:吨)
-- 通过关联dbo.ODS_T5_HJLY_VXSDD_LIST表获取dzrq，统计llwt重量并转换为吨
SELECT
    -- 建材发展中心数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '建材业务服务中心'
            AND CONVERT(date, b.dzrq) >= CONVERT(date, DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0))
            AND CONVERT(date, b.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 建材发展中心_本周_发货量,

    -- 海外事业发展中心数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '海外事业发展'
            AND CONVERT(date, b.dzrq) >= CONVERT(date, DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0))
            AND CONVERT(date, b.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 海外事业发展中心_本周_发货量,

    -- 新材发展中心数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '新材料业务部'
            AND CONVERT(date, b.dzrq) >= CONVERT(date, DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0))
            AND CONVERT(date, b.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 新材发展中心_本周_发货量

