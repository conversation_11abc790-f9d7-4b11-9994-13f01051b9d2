-- 2025年月接单分析报表 - 可拖拽变量格式
-- 生成独立的变量，便于帆软表格单独拖拽
-- 数据来源：dbo.ODS_T5_HJLY_VXSDD_LIST
-- 统计理重总量，单位：吨

SELECT
    -- 2025年各月份数据
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 1 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-1',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 2 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-2',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 3 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-3',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 4 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-4',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 5 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-5',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 6 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-6',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 7 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-7',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 8 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-8',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 9 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-9',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 10 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-10',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 11 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-11',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 12 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-12'
FROM dbo.ODS_T5_HJLY_VXSDD_LIST
WHERE YEAR(dzrq) = 2025
    AND llwt IS NOT NULL
    AND llwt > 0;