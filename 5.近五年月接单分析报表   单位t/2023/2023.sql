-- 2023年月接单分析报表 - 可拖拽变量格式
-- 生成独立的变量，便于帆软表格单独拖拽
-- 数据来源：dbo.ODS_T5_HJLY_VXSDD_LIST
-- 统计理重总量，单位：吨

SELECT
    -- 2023年各月份数据
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 1 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-1',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 2 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-2',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 3 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-3',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 4 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-4',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 5 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-5',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 6 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-6',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 7 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-7',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 8 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-8',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 9 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-9',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 10 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-10',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 11 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-11',
    CAST(ROUND(ISNULL(SUM(CASE WHEN MONTH(dzrq) = 12 THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2023-12'
FROM dbo.ODS_T5_HJLY_VXSDD_LIST
WHERE YEAR(dzrq) = 2023
    AND llwt IS NOT NULL
    AND llwt > 0;