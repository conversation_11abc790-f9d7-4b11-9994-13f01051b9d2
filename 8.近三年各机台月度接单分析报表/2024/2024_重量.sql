-- 2024年各机台月度接单分析报表 - 完整表格结构
-- 生成标准的行列结构，便于帆软直接使用，无需拖拽变量
-- 数据来源：dbo.ODS_T5_HJLY_VXSDD_LIST
-- 统计理重总量，单位：吨

WITH MachineTypes AS (
    -- 定义所有机台类型
    SELECT '600T' AS machine_type, '%600吨%' AS pattern
    UNION ALL SELECT '1000T', '%1000吨%'
    UNION ALL SELECT '1250T', '%1250吨%'
    UNION ALL SELECT '1450T', '%1450吨%'
    UNION ALL SELECT '2000T', '%2000吨%'
    UNION ALL SELECT '2200T', '%2200吨%'
    UNION ALL SELECT '2600T', '%2600吨%'
    UNION ALL SELECT '3300T', '%3300吨%'
    UNION ALL SELECT '3600T', '%3600吨%'
    UNION ALL SELECT '4500T', '%4500吨%'
    UNION ALL SELECT '5600T', '%5600吨%'
    UNION ALL SELECT '7500T', '%7500吨%'
    UNION ALL SELECT '10000T', '%10000吨%'
),
MonthlyData AS (
    -- 计算每种机台类型的月度数据
    SELECT
        mt.machine_type,
        MONTH(dzrq) AS month_num,
        DATENAME(MONTH, dzrq) AS month_name,
        CAST(ROUND(ISNULL(SUM(llwt) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS weight_tons
    FROM dbo.ODS_T5_HJLY_VXSDD_LIST data
    CROSS JOIN MachineTypes mt
    WHERE YEAR(dzrq) = 2024
        AND worklbmc IS NOT NULL
        AND llwt IS NOT NULL
        AND llwt > 0
        AND worklbmc LIKE mt.pattern
    GROUP BY mt.machine_type, MONTH(dzrq), DATENAME(MONTH, dzrq)
)

-- 生成透视表结构：行为机台类型，列为月份
SELECT
    machine_type AS '机台类型',
    ISNULL([1], 0.00) AS '1月',
    ISNULL([2], 0.00) AS '2月',
    ISNULL([3], 0.00) AS '3月',
    ISNULL([4], 0.00) AS '4月',
    ISNULL([5], 0.00) AS '5月',
    ISNULL([6], 0.00) AS '6月',
    ISNULL([7], 0.00) AS '7月',
    ISNULL([8], 0.00) AS '8月',
    ISNULL([9], 0.00) AS '9月',
    ISNULL([10], 0.00) AS '10月',
    ISNULL([11], 0.00) AS '11月',
    ISNULL([12], 0.00) AS '12月',
    -- 计算年度总计
    (ISNULL([1], 0) + ISNULL([2], 0) + ISNULL([3], 0) + ISNULL([4], 0) +
     ISNULL([5], 0) + ISNULL([6], 0) + ISNULL([7], 0) + ISNULL([8], 0) +
     ISNULL([9], 0) + ISNULL([10], 0) + ISNULL([11], 0) + ISNULL([12], 0)) AS '年度总计'
FROM (
    SELECT machine_type, month_num, weight_tons
    FROM MonthlyData
) AS SourceTable
PIVOT (
    SUM(weight_tons)
    FOR month_num IN ([1], [2], [3], [4], [5], [6], [7], [8], [9], [10], [11], [12])
) AS PivotTable
ORDER BY
    CASE machine_type
        WHEN '600T' THEN 1
        WHEN '1000T' THEN 2
        WHEN '1250T' THEN 3
        WHEN '1450T' THEN 4
        WHEN '2000T' THEN 5
        WHEN '2200T' THEN 6
        WHEN '2600T' THEN 7
        WHEN '3300T' THEN 8
        WHEN '3600T' THEN 9
        WHEN '4500T' THEN 10
        WHEN '5600T' THEN 11
        WHEN '7500T' THEN 12
        WHEN '10000T' THEN 13
        ELSE 99
    END;
