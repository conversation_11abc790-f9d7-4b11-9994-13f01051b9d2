-- 2021年接单结构（后工序）分析报表 - 可拖拽变量格式
-- 生成独立的变量，便于帆软表格单独拖拽
-- 数据来源：dbo.ODS_T5_HJLY_VXSDD_LIST
-- 统计理重总量，单位：吨

SELECT
    -- 2021-基材
    CAST(ROUND(ISNULL(SUM(CASE WHEN yslbName0 LIKE '%基材%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2021-基材',

    -- 2021-氧化
    CAST(ROUND(ISNULL(SUM(CASE WHEN yslbName0 LIKE '%氧化%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2021-氧化',

    -- 2021-电泳
    CAST(ROUND(ISNULL(SUM(CASE WHEN yslbName0 LIKE '%电泳%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2021-电泳',

    -- 2021-喷粉
    CAST(ROUND(ISNULL(SUM(CASE WHEN yslbName0 LIKE '%喷粉%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2021-喷粉',

    -- 2021-喷涂木纹
    CAST(ROUND(ISNULL(SUM(CASE WHEN yslbName0 LIKE '%喷涂木纹%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2021-喷涂木纹',

    -- 2021-木纹转印
    CAST(ROUND(ISNULL(SUM(CASE WHEN yslbName0 LIKE '%木纹转印%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2021-木纹转印',

    -- 2021-3D木纹
    CAST(ROUND(ISNULL(SUM(CASE WHEN yslbName0 LIKE '%3D木纹%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2021-3D木纹',

    -- 2021-氟碳
    CAST(ROUND(ISNULL(SUM(CASE WHEN yslbName0 LIKE '%氟碳%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2021-氟碳',

    -- 2021-注胶
    CAST(ROUND(ISNULL(SUM(CASE WHEN yslbName0 LIKE '%注胶%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2021-注胶',

    -- 2021-隔热
    CAST(ROUND(ISNULL(SUM(CASE WHEN yslbName0 LIKE '%隔热%' AND yslbName0 NOT LIKE '%喷涂木纹%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2021-隔热'

FROM dbo.ODS_T5_HJLY_VXSDD_LIST
WHERE YEAR(dzrq) = 2021
    AND yslbName0 IS NOT NULL
    AND llwt IS NOT NULL
    AND llwt > 0
    AND (yslbName0 LIKE '%基材%' OR yslbName0 LIKE '%氧化%' OR yslbName0 LIKE '%电泳%'
         OR yslbName0 LIKE '%喷粉%' OR yslbName0 LIKE '%喷涂木纹%' OR yslbName0 LIKE '%木纹转印%'
         OR yslbName0 LIKE '%3D木纹%' OR yslbName0 LIKE '%氟碳%' OR yslbName0 LIKE '%注胶%'
         OR (yslbName0 LIKE '%隔热%' AND yslbName0 NOT LIKE '%喷涂木纹%'));
