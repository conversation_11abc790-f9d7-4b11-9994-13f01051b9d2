-- 2024年各表面处理月度接单分析报表 - 完整表格结构
-- 生成标准的行列结构，便于帆软直接使用，无需拖拽变量
-- 数据来源：dbo.ODS_T5_HJLY_VXSDD_LIST
-- 统计理重总量，单位：吨

WITH SurfaceTypes AS (
    -- 定义所有表面处理类型
    SELECT '基材' AS surface_type, '%基材%' AS pattern
    UNION ALL SELECT '氧化', '%氧化%'
    UNION ALL SELECT '电泳', '%电泳%'
    UNION ALL SELECT '喷粉', '%喷粉%'
    UNION ALL SELECT '喷涂木纹', '%喷涂木纹%'
    UNION ALL SELECT '木纹转印', '%木纹转印%'
    UNION ALL SELECT '3D木纹', '%3D木纹%'
    UNION ALL SELECT '氟碳', '%氟碳%'
    UNION ALL SELECT '注胶', '%注胶%'
    UNION ALL SELECT '隔热', '%隔热%'
),
MonthlyData AS (
    -- 计算每种表面处理类型的月度数据
    SELECT
        st.surface_type,
        MONTH(dzrq) AS month_num,
        DATENAME(MONTH, dzrq) AS month_name,
        CAST(ROUND(ISNULL(SUM(llwt) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS weight_tons
    FROM dbo.ODS_T5_HJLY_VXSDD_LIST data
    CROSS JOIN SurfaceTypes st
    WHERE YEAR(dzrq) = 2024
        AND yslbName0 IS NOT NULL
        AND llwt IS NOT NULL
        AND llwt > 0
        AND (
            (st.surface_type = '隔热' AND yslbName0 LIKE st.pattern AND yslbName0 NOT LIKE '%喷涂木纹%')
            OR (st.surface_type != '隔热' AND yslbName0 LIKE st.pattern)
        )
    GROUP BY st.surface_type, MONTH(dzrq), DATENAME(MONTH, dzrq)
)

-- 生成透视表结构：行为表面处理类型，列为月份
SELECT
    surface_type AS '表面处理类型',
    ISNULL([1], 0.00) AS '1月',
    ISNULL([2], 0.00) AS '2月',
    ISNULL([3], 0.00) AS '3月',
    ISNULL([4], 0.00) AS '4月',
    ISNULL([5], 0.00) AS '5月',
    ISNULL([6], 0.00) AS '6月',
    ISNULL([7], 0.00) AS '7月',
    ISNULL([8], 0.00) AS '8月',
    ISNULL([9], 0.00) AS '9月',
    ISNULL([10], 0.00) AS '10月',
    ISNULL([11], 0.00) AS '11月',
    ISNULL([12], 0.00) AS '12月',
    -- 计算年度总计
    (ISNULL([1], 0) + ISNULL([2], 0) + ISNULL([3], 0) + ISNULL([4], 0) +
     ISNULL([5], 0) + ISNULL([6], 0) + ISNULL([7], 0) + ISNULL([8], 0) +
     ISNULL([9], 0) + ISNULL([10], 0) + ISNULL([11], 0) + ISNULL([12], 0)) AS '年度总计'
FROM (
    SELECT surface_type, month_num, weight_tons
    FROM MonthlyData
) AS SourceTable
PIVOT (
    SUM(weight_tons)
    FOR month_num IN ([1], [2], [3], [4], [5], [6], [7], [8], [9], [10], [11], [12])
) AS PivotTable
ORDER BY
    CASE surface_type
        WHEN '基材' THEN 1
        WHEN '氧化' THEN 2
        WHEN '电泳' THEN 3
        WHEN '喷粉' THEN 4
        WHEN '喷涂木纹' THEN 5
        WHEN '木纹转印' THEN 6
        WHEN '3D木纹' THEN 7
        WHEN '氟碳' THEN 8
        WHEN '注胶' THEN 9
        WHEN '隔热' THEN 10
        ELSE 99
    END;
