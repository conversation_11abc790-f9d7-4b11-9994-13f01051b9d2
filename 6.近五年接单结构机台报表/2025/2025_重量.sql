-- 2025年接单结构机台报表 - 可拖拽变量格式
-- 生成独立的变量，便于帆软表格单独拖拽
-- 数据来源：dbo.ODS_T5_HJLY_VXSDD_LIST
-- 统计理重总量，单位：吨

SELECT
    -- 2025-600T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%600吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-600T',

    -- 2025-1000T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%1000吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-1000T',

    -- 2025-1250T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%1250吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-1250T',

    -- 2025-1450T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%1450吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-1450T',

    -- 2025-2000T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%2000吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-2000T',

    -- 2025-2200T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%2200吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-2200T',

    -- 2025-2600T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%2600吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-2600T',

    -- 2025-3300T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%3300吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-3300T',

    -- 2025-3600T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%3600吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-3600T',

    -- 2025-4500T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%4500吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-4500T',

    -- 2025-5600T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%5600吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-5600T',

    -- 2025-7500T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%7500吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-7500T',

    -- 2025-10000T
    CAST(ROUND(ISNULL(SUM(CASE WHEN worklbmc LIKE '%10000吨%' THEN llwt END) / 1000.0, 0), 2) AS DECIMAL(18,2)) AS '2025-10000T'

FROM dbo.ODS_T5_HJLY_VXSDD_LIST
WHERE YEAR(dzrq) = 2025
    AND worklbmc IS NOT NULL
    AND llwt IS NOT NULL
    AND llwt > 0
    AND (worklbmc LIKE '%600吨%' OR worklbmc LIKE '%1000吨%' OR worklbmc LIKE '%1250吨%'
         OR worklbmc LIKE '%1450吨%' OR worklbmc LIKE '%2000吨%' OR worklbmc LIKE '%2200吨%'
         OR worklbmc LIKE '%2600吨%' OR worklbmc LIKE '%3300吨%' OR worklbmc LIKE '%3600吨%'
         OR worklbmc LIKE '%4500吨%' OR worklbmc LIKE '%5600吨%' OR worklbmc LIKE '%7500吨%'
         OR worklbmc LIKE '%10000吨%');
