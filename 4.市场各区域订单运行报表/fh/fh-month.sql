-- 市场各区域本月发货报表 - 理重总量统计(单位:吨)
-- 通过关联dbo.ODS_T5_HJLY_VXSDD_LIST表获取dzrq，统计llwt重量并转换为吨
SELECT
    -- 东部大区数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '东部大区'
            AND CONVERT(date, b.dzrq) >= CONVERT(date, DATEADD(month, DATEDIFF(month, 0, GETDATE()), 0))
            AND CONVERT(date, b.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 东部大区_本月_发货量,

    -- 西部大区数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '西部大区'
            AND CONVERT(date, b.dzrq) >= CONVERT(date, DATEADD(month, DATEDIFF(month, 0, GETDATE()), 0))
            AND CONVERT(date, b.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 西部大区_本月_发货量,

    -- 南部大区数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '南部大区'
            AND CONVERT(date, b.dzrq) >= CONVERT(date, DATEADD(month, DATEDIFF(month, 0, GETDATE()), 0))
            AND CONVERT(date, b.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 南部大区_本月_发货量,

    -- 海外事业发展数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name = '海外事业发展'
            AND CONVERT(date, b.dzrq) >= CONVERT(date, DATEADD(month, DATEDIFF(month, 0, GETDATE()), 0))
            AND CONVERT(date, b.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 海外事业发展_本月_发货量,

    -- 新材各区域及部门汇总数据
    ISNULL((SELECT ROUND(SUM(a.llwt) / 1000.0, 2) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE a.Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域')
            AND CONVERT(date, b.dzrq) >= CONVERT(date, DATEADD(month, DATEDIFF(month, 0, GETDATE()), 0))
            AND CONVERT(date, b.dzrq) <= CONVERT(date, GETDATE())
            AND a.llwt IS NOT NULL AND a.llwt > 0), 0) AS 新材各区域及部门_本月_发货量

