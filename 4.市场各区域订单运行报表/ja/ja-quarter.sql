-- 市场各区域本季度结案库存报表 - 按结案天数区间统计 - 优化版本
-- 根据生产结案天数yjaDays划分区间：<30天、30-90天、90-180天、>180天
-- 通过关联dbo.ODS_T5_HJLY_VXSDD_LIST表获取dzrq日期信息
-- 优化说明：使用单次JOIN+条件聚合替代20个子查询，避免函数导致的索引失效
WITH DateRange AS (
    -- 预计算本季度日期范围，避免重复计算
    SELECT 
        DATEADD(quarter, DATEDIFF(quarter, 0, GETDATE()), 0) AS quarter_start,
        DATEADD(day, -1, DATEADD(quarter, DATEDIFF(quarter, 0, GETDATE()) + 1, 0)) AS quarter_end
)
SELECT
    -- 东部大区数据
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '东部大区' AND a.yjaDays < 30 THEN a.dzid END), 0) AS 东部大区_本季度_结案库存_小于30天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '东部大区' AND a.yjaDays >= 30 AND a.yjaDays < 90 THEN a.dzid END), 0) AS 东部大区_本季度_结案库存_30至90天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '东部大区' AND a.yjaDays >= 90 AND a.yjaDays < 180 THEN a.dzid END), 0) AS 东部大区_本季度_结案库存_90至180天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '东部大区' AND a.yjaDays >= 180 THEN a.dzid END), 0) AS 东部大区_本季度_结案库存_大于180天,

    -- 西部大区数据
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '西部大区' AND a.yjaDays < 30 THEN a.dzid END), 0) AS 西部大区_本季度_结案库存_小于30天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '西部大区' AND a.yjaDays >= 30 AND a.yjaDays < 90 THEN a.dzid END), 0) AS 西部大区_本季度_结案库存_30至90天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '西部大区' AND a.yjaDays >= 90 AND a.yjaDays < 180 THEN a.dzid END), 0) AS 西部大区_本季度_结案库存_90至180天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '西部大区' AND a.yjaDays >= 180 THEN a.dzid END), 0) AS 西部大区_本季度_结案库存_大于180天,

    -- 南部大区数据
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '南部大区' AND a.yjaDays < 30 THEN a.dzid END), 0) AS 南部大区_本季度_结案库存_小于30天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '南部大区' AND a.yjaDays >= 30 AND a.yjaDays < 90 THEN a.dzid END), 0) AS 南部大区_本季度_结案库存_30至90天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '南部大区' AND a.yjaDays >= 90 AND a.yjaDays < 180 THEN a.dzid END), 0) AS 南部大区_本季度_结案库存_90至180天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '南部大区' AND a.yjaDays >= 180 THEN a.dzid END), 0) AS 南部大区_本季度_结案库存_大于180天,

    -- 海外事业发展数据
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '海外事业发展' AND a.yjaDays < 30 THEN a.dzid END), 0) AS 海外事业发展_本季度_结案库存_小于30天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '海外事业发展' AND a.yjaDays >= 30 AND a.yjaDays < 90 THEN a.dzid END), 0) AS 海外事业发展_本季度_结案库存_30至90天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '海外事业发展' AND a.yjaDays >= 90 AND a.yjaDays < 180 THEN a.dzid END), 0) AS 海外事业发展_本季度_结案库存_90至180天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '海外事业发展' AND a.yjaDays >= 180 THEN a.dzid END), 0) AS 海外事业发展_本季度_结案库存_大于180天,

    -- 新材各区域及部门汇总数据
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') 
                               AND a.yjaDays < 30 THEN a.dzid END), 0) AS 新材各区域及部门_本季度_结案库存_小于30天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') 
                               AND a.yjaDays >= 30 AND a.yjaDays < 90 THEN a.dzid END), 0) AS 新材各区域及部门_本季度_结案库存_30至90天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') 
                               AND a.yjaDays >= 90 AND a.yjaDays < 180 THEN a.dzid END), 0) AS 新材各区域及部门_本季度_结案库存_90至180天,
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') 
                               AND a.yjaDays >= 180 THEN a.dzid END), 0) AS 新材各区域及部门_本季度_结案库存_大于180天

FROM 
    dbo.ODS_T5_vcpkc_cczs_xsfh a
    INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
    CROSS JOIN DateRange
WHERE 
    b.dzrq >= DateRange.quarter_start
    AND b.dzrq <= DateRange.quarter_end
    AND a.Dept_Name IN ('东部大区', '西部大区', '南部大区', '海外事业发展', 
                       '新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', 
                       '新材料业务部', '新材山东区域', '新材西部区域')
