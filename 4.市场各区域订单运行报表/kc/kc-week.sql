-- 各市场区域本周库存报表 - 按库存天数区间统计 - 优化版本
-- 使用JOIN关联dbo.ODS_T5_HJLY_VCPKC_CCZS和dbo.ODS_T5_HJLY_VXSDD_LIST表，并添加本周时间条件
-- 优化说明：使用单次JOIN+条件聚合替代多个子查询，避免函数导致的索引失效
WITH DateRange AS (
    -- 预计算本周日期范围，避免重复计算
    SELECT
        DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0) AS week_start,
        DATEADD(day, 6, DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0)) AS week_end
)
SELECT
    -- 东部大区数据
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '东部大区' THEN a.dzid END), 0) AS 东部大区_本周_库存,

    -- 西部大区数据
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '西部大区' THEN a.dzid END), 0) AS 西部大区_本周_库存,

    -- 南部大区数据
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '南部大区' THEN a.dzid END), 0) AS 南部大区_本周_库存,

    -- 海外事业发展数据
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name = '海外事业发展' THEN a.dzid END), 0) AS 海外事业发展_本周_库存,

    -- 新材各区域及部门汇总数据
    ISNULL(COUNT(DISTINCT CASE WHEN a.Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域')
                               THEN a.dzid END), 0) AS 新材各区域及部门_本周_库存

FROM
    dbo.ODS_T5_HJLY_VCPKC_CCZS a
    INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
    CROSS JOIN DateRange
WHERE
    b.dzrq >= DateRange.week_start
    AND b.dzrq <= DateRange.week_end
    AND a.Dept_Name IN ('东部大区', '西部大区', '南部大区', '海外事业发展',
                       '新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域',
                       '新材料业务部', '新材山东区域', '新材西部区域')