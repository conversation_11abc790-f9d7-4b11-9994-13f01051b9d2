-- 市场各区域本月订单运行报表 - 基础数据版本
-- 包含：接单量、暂不排产、签批量
SELECT
    -- 东部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '东部大区' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 东部大区_本月_接单量,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '东部大区' AND ztbit = 'true' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 东部大区_本月_暂不排产,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '东部大区' AND pzbz = 'true' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 东部大区_本月_签批量,

    -- 西部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '西部大区' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 西部大区_本月_接单量,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '西部大区' AND ztbit = 'true' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 西部大区_本月_暂不排产,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '西部大区' AND pzbz = 'true' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 西部大区_本月_签批量,

    -- 南部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '南部大区' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 南部大区_本月_接单量,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '南部大区' AND ztbit = 'true' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 南部大区_本月_暂不排产,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '南部大区' AND pzbz = 'true' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 南部大区_本月_签批量,

    -- 海外事业发展数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '海外事业发展' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 海外事业发展_本月_接单量,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '海外事业发展' AND ztbit = 'true' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 海外事业发展_本月_暂不排产,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name = '海外事业发展' AND pzbz = 'true' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 海外事业发展_本月_签批量,

    -- 新材各区域及部门汇总数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域')
            AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 新材各区域及部门_本月_接单量,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域')
            AND ztbit = 'true' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 新材各区域及部门_本月_暂不排产,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSDD_LIST
            WHERE Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域')
            AND pzbz = 'true' AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 新材各区域及部门_本月_签批量

