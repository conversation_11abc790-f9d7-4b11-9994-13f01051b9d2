-- 市场各区域本周订单运行报表 - 优化版本
-- 包含：接单量、暂不排产、签批量
-- 优化说明：使用单次扫描+条件聚合替代多个子查询，避免函数导致的索引失效
WITH DateRange AS (
    -- 预计算本周日期范围，避免重复计算
    SELECT
        DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0) AS week_start,
        DATEADD(day, 6, DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0)) AS week_end
)
SELECT
    -- 东部大区数据
    ISNULL(SUM(CASE WHEN Dept_Name = '东部大区' THEN 1 ELSE 0 END), 0) AS 东部大区_本周_接单量,
    ISNULL(SUM(CASE WHEN Dept_Name = '东部大区' AND ztbit = 'true' THEN 1 ELSE 0 END), 0) AS 东部大区_本周_暂不排产,
    ISNULL(SUM(CASE WHEN Dept_Name = '东部大区' AND pzbz = 'true' THEN 1 ELSE 0 END), 0) AS 东部大区_本周_签批量,

    -- 西部大区数据
    ISNULL(SUM(CASE WHEN Dept_Name = '西部大区' THEN 1 ELSE 0 END), 0) AS 西部大区_本周_接单量,
    ISNULL(SUM(CASE WHEN Dept_Name = '西部大区' AND ztbit = 'true' THEN 1 ELSE 0 END), 0) AS 西部大区_本周_暂不排产,
    ISNULL(SUM(CASE WHEN Dept_Name = '西部大区' AND pzbz = 'true' THEN 1 ELSE 0 END), 0) AS 西部大区_本周_签批量,

    -- 南部大区数据
    ISNULL(SUM(CASE WHEN Dept_Name = '南部大区' THEN 1 ELSE 0 END), 0) AS 南部大区_本周_接单量,
    ISNULL(SUM(CASE WHEN Dept_Name = '南部大区' AND ztbit = 'true' THEN 1 ELSE 0 END), 0) AS 南部大区_本周_暂不排产,
    ISNULL(SUM(CASE WHEN Dept_Name = '南部大区' AND pzbz = 'true' THEN 1 ELSE 0 END), 0) AS 南部大区_本周_签批量,

    -- 海外事业发展数据
    ISNULL(SUM(CASE WHEN Dept_Name = '海外事业发展' THEN 1 ELSE 0 END), 0) AS 海外事业发展_本周_接单量,
    ISNULL(SUM(CASE WHEN Dept_Name = '海外事业发展' AND ztbit = 'true' THEN 1 ELSE 0 END), 0) AS 海外事业发展_本周_暂不排产,
    ISNULL(SUM(CASE WHEN Dept_Name = '海外事业发展' AND pzbz = 'true' THEN 1 ELSE 0 END), 0) AS 海外事业发展_本周_签批量,

    -- 新材各区域及部门汇总数据
    ISNULL(SUM(CASE WHEN Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') THEN 1 ELSE 0 END), 0) AS 新材各区域及部门_本周_接单量,
    ISNULL(SUM(CASE WHEN Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') AND ztbit = 'true' THEN 1 ELSE 0 END), 0) AS 新材各区域及部门_本周_暂不排产,
    ISNULL(SUM(CASE WHEN Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') AND pzbz = 'true' THEN 1 ELSE 0 END), 0) AS 新材各区域及部门_本周_签批量

FROM (
    -- 使用子查询去重，避免重复计算
    SELECT DISTINCT dzid, Dept_Name, ztbit, pzbz
    FROM dbo.ODS_T5_HJLY_VXSDD_LIST, DateRange
    WHERE dzrq >= DateRange.week_start
      AND dzrq <= DateRange.week_end
      AND Dept_Name IN ('东部大区', '西部大区', '南部大区', '海外事业发展',
                       '新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域',
                       '新材料业务部', '新材山东区域', '新材西部区域')
) AS filtered_data

