
-- 市场各区域本月计划物控中心存单报表 - 独立变量版本
SELECT
    -- 东部大区计划物控中心存单
    ISNULL((SELECT COUNT(*) FROM dbo.ODS_T5_vscdd_dzbh
            WHERE Dept_Name = '东部大区' AND pzbz = 'false' AND stoped = 'false' AND canceled = 'false'
            AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 东部大区_本月_计划物控中心存单,

    -- 西部大区计划物控中心存单
    ISNULL((SELECT COUNT(*) FROM dbo.ODS_T5_vscdd_dzbh
            WHERE Dept_Name = '西部大区' AND pzbz = 'false' AND stoped = 'false' AND canceled = 'false'
            AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 西部大区_本月_计划物控中心存单,

    -- 南部大区计划物控中心存单
    ISNULL((SELECT COUNT(*) FROM dbo.ODS_T5_vscdd_dzbh
            WHERE Dept_Name = '南部大区' AND pzbz = 'false' AND stoped = 'false' AND canceled = 'false'
            AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 南部大区_本月_计划物控中心存单,

    -- 海外事业发展计划物控中心存单
    ISNULL((SELECT COUNT(*) FROM dbo.ODS_T5_vscdd_dzbh
            WHERE Dept_Name = '海外事业发展' AND pzbz = 'false' AND stoped = 'false' AND canceled = 'false'
            AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 海外事业发展_本月_计划物控中心存单,

    -- 新材各区域及部门汇总计划物控中心存单
    ISNULL((SELECT COUNT(*) FROM dbo.ODS_T5_vscdd_dzbh
            WHERE Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域')
            AND pzbz = 'false' AND stoped = 'false' AND canceled = 'false'
            AND MONTH(dzrq) = MONTH(GETDATE()) AND YEAR(dzrq) = YEAR(GETDATE())), 0) AS 新材各区域及部门_本月_计划物控中心存单

